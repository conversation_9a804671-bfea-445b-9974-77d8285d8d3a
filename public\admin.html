<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickDeliver - Admin Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: #1e293b;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid #334155;
        }

        .logo {
            display: flex;
            align-items: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .logo i {
            margin-right: 0.75rem;
            color: #3b82f6;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            display: block;
            padding: 0.875rem 1.5rem;
            color: #94a3b8;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .nav-item:hover,
        .nav-item.active {
            background: #334155;
            color: white;
        }

        .nav-item i {
            width: 20px;
            margin-right: 0.75rem;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }

        .header {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1e293b;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        /* Dashboard Content */
        .dashboard-content {
            padding: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-icon.blue { background: #dbeafe; color: #3b82f6; }
        .stat-icon.green { background: #dcfce7; color: #16a34a; }
        .stat-icon.yellow { background: #fef3c7; color: #d97706; }
        .stat-icon.red { background: #fee2e2; color: #dc2626; }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.875rem;
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .stat-change.positive { color: #16a34a; }
        .stat-change.negative { color: #dc2626; }

        /* Charts Section */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: white;
            padding: 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .chart-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
        }

        /* Orders Table */
        .orders-section {
            background: white;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }

        .table-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
        }

        .table-filters {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
        }

        .filter-select {
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            background: white;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: #f8fafc;
            padding: 0.75rem 1.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: #374151;
            border-bottom: 1px solid #e2e8f0;
        }

        td {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            font-size: 0.875rem;
        }

        tr:hover {
            background: #f8fafc;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }

        .status-order_placed { background: #fef3c7; color: #92400e; }
        .status-picked_up { background: #dbeafe; color: #1e40af; }
        .status-in_transit { background: #e0e7ff; color: #3730a3; }
        .status-delivered { background: #dcfce7; color: #166534; }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border-radius: 0.375rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .table-filters {
                flex-direction: column;
            }

            .search-input {
                min-width: auto;
            }
        }

        /* Loading States */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            color: #64748b;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-shipping-fast"></i>
                QuickDeliver
            </div>
        </div>
        <nav class="sidebar-nav">
            <a href="#" class="nav-item active" onclick="showSection('dashboard')">
                <i class="fas fa-chart-line"></i>
                Dashboard
            </a>
            <a href="#" class="nav-item" onclick="showSection('orders')">
                <i class="fas fa-box"></i>
                Orders
            </a>
            <a href="#" class="nav-item" onclick="showSection('analytics')">
                <i class="fas fa-chart-bar"></i>
                Analytics
            </a>
            <a href="#" class="nav-item" onclick="showSection('settings')">
                <i class="fas fa-cog"></i>
                Settings
            </a>
            <a href="/" class="nav-item">
                <i class="fas fa-home"></i>
                Main Site
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="header">
            <h1 class="header-title">Dashboard</h1>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <button class="btn btn-primary" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon blue">
                            <i class="fas fa-box"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="totalOrders">0</div>
                    <div class="stat-label">Total Orders</div>
                    <div class="stat-change positive" id="ordersChange">+0% from last week</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon green">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="totalRevenue">₹0</div>
                    <div class="stat-label">Total Revenue</div>
                    <div class="stat-change positive" id="revenueChange">+0% from last week</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon yellow">
                            <i class="fas fa-truck"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="activeDeliveries">0</div>
                    <div class="stat-label">Active Deliveries</div>
                    <div class="stat-change" id="activeChange">Currently in transit</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon red">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value" id="completedToday">0</div>
                    <div class="stat-label">Completed Today</div>
                    <div class="stat-change positive" id="completedChange">+0% from yesterday</div>
                </div>
            </div>

            <!-- Enhanced Charts Section -->
            <div class="charts-grid">
                <!-- Orders Overview Chart -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Orders Overview</h3>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <select class="filter-select" id="chartPeriod" onchange="updateOrdersChart()">
                                <option value="7">Last 7 days</option>
                                <option value="14">Last 14 days</option>
                                <option value="30">Last 30 days</option>
                                <option value="90">Last 3 months</option>
                            </select>
                            <select class="filter-select" id="chartType" onchange="updateOrdersChart()">
                                <option value="orders">Orders Count</option>
                                <option value="revenue">Revenue</option>
                                <option value="both">Both</option>
                            </select>
                        </div>
                    </div>

                    <!-- Chart Summary Stats -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-bottom: 1.5rem; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                        <div style="text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: #3b82f6;" id="chartTotalOrders">0</div>
                            <div style="font-size: 0.75rem; color: #64748b;">Total Orders</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: #10b981;" id="chartTotalRevenue">₹0</div>
                            <div style="font-size: 0.75rem; color: #64748b;">Total Revenue</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: 700; color: #f59e0b;" id="chartAvgOrder">₹0</div>
                            <div style="font-size: 0.75rem; color: #64748b;">Avg Order Value</div>
                        </div>
                    </div>

                    <canvas id="ordersChart" height="300"></canvas>
                </div>

                <!-- Enhanced Order Status Section -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Order Status Distribution</h3>
                        <button class="btn btn-secondary btn-sm" onclick="refreshStatusChart()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>

                    <!-- Status Summary -->
                    <div style="margin-bottom: 1.5rem;">
                        <div id="statusSummary" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 0.75rem;">
                            <!-- Status items will be populated here -->
                        </div>
                    </div>

                    <canvas id="statusChart" height="250"></canvas>

                    <!-- Status Progress Indicators -->
                    <div style="margin-top: 1.5rem;">
                        <h4 style="font-size: 0.875rem; font-weight: 600; color: #374151; margin-bottom: 1rem;">Delivery Performance</h4>
                        <div id="performanceMetrics">
                            <!-- Performance metrics will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Analytics Row -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                <!-- Delivery Time Analytics -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Delivery Time Analysis</h3>
                    </div>
                    <canvas id="deliveryTimeChart" height="200"></canvas>
                </div>

                <!-- Peak Hours Chart -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Peak Order Hours</h3>
                    </div>
                    <canvas id="peakHoursChart" height="200"></canvas>
                </div>

                <!-- Package Type Distribution -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">Package Types</h3>
                    </div>
                    <canvas id="packageTypeChart" height="200"></canvas>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="orders-section">
                <div class="table-header">
                    <h3 class="table-title">Recent Orders</h3>
                    <div class="header-actions">
                        <button class="btn btn-secondary btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="table-filters">
                    <input type="text" class="search-input" id="searchBox" placeholder="Search orders...">
                    <select class="filter-select" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="order_placed">Order Placed</option>
                        <option value="picked_up">Picked Up</option>
                        <option value="in_transit">In Transit</option>
                        <option value="delivered">Delivered</option>
                    </select>
                    <select class="filter-select" id="dateFilter">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                    </select>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Route</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <tr>
                                <td colspan="7" class="loading">
                                    <div class="spinner"></div>
                                    Loading orders...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // Global variables
        let allOrders = [];
        let ordersChart = null;
        let statusChart = null;
        let currentSection = 'dashboard';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadOrders();
            setupEventListeners();
        });

        // Load orders from API
        async function loadOrders() {
            try {
                const response = await fetch('/api/orders');
                const data = await response.json();

                if (data.success) {
                    allOrders = data.orders;
                    updateDashboard();
                }
            } catch (error) {
                console.error('Error loading orders:', error);
                showNotification('Failed to load orders', 'error');
            }
        }

        // Update entire dashboard
        function updateDashboard() {
            updateStats();
            updateCharts();
            displayOrders();
        }

        // Update statistics cards
        function updateStats() {
            const stats = calculateStats(allOrders);

            document.getElementById('totalOrders').textContent = stats.totalOrders;
            document.getElementById('totalRevenue').textContent = `₹${stats.totalRevenue.toLocaleString()}`;
            document.getElementById('activeDeliveries').textContent = stats.activeDeliveries;
            document.getElementById('completedToday').textContent = stats.completedToday;

            // Update change indicators
            document.getElementById('ordersChange').textContent = `+${stats.ordersGrowth}% from last week`;
            document.getElementById('revenueChange').textContent = `+${stats.revenueGrowth}% from last week`;
            document.getElementById('completedChange').textContent = `+${stats.completedGrowth}% from yesterday`;
        }

        // Calculate statistics
        function calculateStats(orders) {
            const today = new Date();
            const todayStr = today.toDateString();
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

            const totalOrders = orders.length;
            const totalRevenue = orders.reduce((sum, order) => sum + order.estimatedCost, 0);
            const activeDeliveries = orders.filter(order =>
                order.status === 'picked_up' || order.status === 'in_transit'
            ).length;
            const completedToday = orders.filter(order =>
                new Date(order.createdAt).toDateString() === todayStr && order.status === 'delivered'
            ).length;

            // Calculate growth (mock data for demo)
            const ordersGrowth = Math.floor(Math.random() * 20) + 5;
            const revenueGrowth = Math.floor(Math.random() * 15) + 8;
            const completedGrowth = Math.floor(Math.random() * 25) + 10;

            return {
                totalOrders,
                totalRevenue,
                activeDeliveries,
                completedToday,
                ordersGrowth,
                revenueGrowth,
                completedGrowth
            };
        }

        // Display orders in table
        function displayOrders(filteredOrders = null) {
            const orders = filteredOrders || allOrders;
            const tbody = document.getElementById('ordersTableBody');

            if (orders.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; color: #64748b;">
                            <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                            No orders found
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = orders.map(order => `
                <tr>
                    <td>
                        <div style="font-weight: 600; color: #1e293b;">${order.trackingId}</div>
                        <div style="font-size: 0.75rem; color: #64748b;">${order.packageType}</div>
                    </td>
                    <td>
                        <div style="font-weight: 500;">${order.contactNumber}</div>
                        <div style="font-size: 0.75rem; color: #64748b;">${order.weight} kg</div>
                    </td>
                    <td>
                        <div style="font-size: 0.75rem; color: #64748b;">From:</div>
                        <div style="margin-bottom: 0.25rem;">${truncateText(order.pickupAddress, 30)}</div>
                        <div style="font-size: 0.75rem; color: #64748b;">To:</div>
                        <div>${truncateText(order.deliveryAddress, 30)}</div>
                    </td>
                    <td style="font-weight: 600; color: #1e293b;">₹${order.estimatedCost.toLocaleString()}</td>
                    <td>
                        <span class="status-badge status-${order.status}">
                            ${formatStatus(order.status)}
                        </span>
                    </td>
                    <td>
                        <div>${formatDate(order.createdAt)}</div>
                        <div style="font-size: 0.75rem; color: #64748b;">${getTimeAgo(order.createdAt)}</div>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-secondary btn-sm" onclick="viewOrderDetails('${order.trackingId}')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="updateOrderStatus('${order.trackingId}')" title="Update Status">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Update charts
        function updateCharts() {
            updateOrdersChart();
            updateStatusChart();
            updateDeliveryTimeChart();
            updatePeakHoursChart();
            updatePackageTypeChart();
        }

        // Enhanced orders chart with multiple metrics
        function updateOrdersChart() {
            const ctx = document.getElementById('ordersChart').getContext('2d');
            const period = parseInt(document.getElementById('chartPeriod').value);
            const chartType = document.getElementById('chartType').value;

            // Prepare data for selected period
            const dates = [];
            const orderCounts = [];
            const revenues = [];

            for (let i = period - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                dates.push(dateStr);

                const dayOrders = allOrders.filter(order =>
                    new Date(order.createdAt).toDateString() === date.toDateString()
                );

                orderCounts.push(dayOrders.length);
                revenues.push(dayOrders.reduce((sum, order) => sum + order.estimatedCost, 0));
            }

            // Update summary stats
            const totalOrders = orderCounts.reduce((sum, count) => sum + count, 0);
            const totalRevenue = revenues.reduce((sum, rev) => sum + rev, 0);
            const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

            document.getElementById('chartTotalOrders').textContent = totalOrders;
            document.getElementById('chartTotalRevenue').textContent = `₹${totalRevenue.toLocaleString()}`;
            document.getElementById('chartAvgOrder').textContent = `₹${Math.round(avgOrderValue).toLocaleString()}`;

            if (ordersChart) {
                ordersChart.destroy();
            }

            let datasets = [];

            if (chartType === 'orders' || chartType === 'both') {
                datasets.push({
                    label: 'Orders',
                    data: orderCounts,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: chartType === 'orders',
                    tension: 0.4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    yAxisID: 'y'
                });
            }

            if (chartType === 'revenue' || chartType === 'both') {
                datasets.push({
                    label: 'Revenue (₹)',
                    data: revenues,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: chartType === 'revenue',
                    tension: 0.4,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    yAxisID: chartType === 'both' ? 'y1' : 'y'
                });
            }

            const scales = {
                x: {
                    grid: { display: false },
                    ticks: { color: '#64748b', maxTicksLimit: 8 }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    grid: { color: '#f1f5f9' },
                    ticks: {
                        color: '#64748b',
                        stepSize: chartType === 'orders' ? 1 : undefined
                    }
                }
            };

            if (chartType === 'both') {
                scales.y1 = {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: { drawOnChartArea: false },
                    ticks: { color: '#64748b' }
                };
            }

            ordersChart = new Chart(ctx, {
                type: 'line',
                data: { labels: dates, datasets: datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            display: chartType === 'both',
                            position: 'top',
                            labels: { color: '#64748b', usePointStyle: true }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#e2e8f0',
                            borderWidth: 1
                        }
                    },
                    scales: scales
                }
            });
        }

        // Enhanced status chart with detailed analytics
        function updateStatusChart() {
            const ctx = document.getElementById('statusChart').getContext('2d');

            const statusCounts = {
                'order_placed': 0,
                'picked_up': 0,
                'in_transit': 0,
                'delivered': 0
            };

            const statusLabels = {
                'order_placed': 'Order Placed',
                'picked_up': 'Picked Up',
                'in_transit': 'In Transit',
                'delivered': 'Delivered'
            };

            const statusColors = {
                'order_placed': '#f59e0b',
                'picked_up': '#3b82f6',
                'in_transit': '#8b5cf6',
                'delivered': '#10b981'
            };

            allOrders.forEach(order => {
                if (statusCounts.hasOwnProperty(order.status)) {
                    statusCounts[order.status]++;
                }
            });

            // Update status summary
            const statusSummary = document.getElementById('statusSummary');
            statusSummary.innerHTML = Object.keys(statusCounts).map(status => {
                const count = statusCounts[status];
                const percentage = allOrders.length > 0 ? Math.round((count / allOrders.length) * 100) : 0;
                return `
                    <div style="display: flex; align-items: center; padding: 0.5rem; background: ${statusColors[status]}15; border-radius: 0.5rem;">
                        <div style="width: 12px; height: 12px; background: ${statusColors[status]}; border-radius: 50%; margin-right: 0.5rem;"></div>
                        <div style="flex: 1;">
                            <div style="font-weight: 600; font-size: 0.875rem; color: #1e293b;">${count}</div>
                            <div style="font-size: 0.75rem; color: #64748b;">${statusLabels[status]} (${percentage}%)</div>
                        </div>
                    </div>
                `;
            }).join('');

            // Update performance metrics
            const totalOrders = allOrders.length;
            const deliveredOrders = statusCounts.delivered;
            const activeOrders = statusCounts.picked_up + statusCounts.in_transit;
            const deliveryRate = totalOrders > 0 ? Math.round((deliveredOrders / totalOrders) * 100) : 0;
            const activeRate = totalOrders > 0 ? Math.round((activeOrders / totalOrders) * 100) : 0;

            document.getElementById('performanceMetrics').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <span style="font-size: 0.75rem; color: #64748b;">Delivery Rate</span>
                            <span style="font-size: 0.75rem; font-weight: 600; color: #10b981;">${deliveryRate}%</span>
                        </div>
                        <div style="background: #f1f5f9; height: 6px; border-radius: 3px; overflow: hidden;">
                            <div style="background: #10b981; height: 100%; width: ${deliveryRate}%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                    <div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <span style="font-size: 0.75rem; color: #64748b;">Active Orders</span>
                            <span style="font-size: 0.75rem; font-weight: 600; color: #3b82f6;">${activeRate}%</span>
                        </div>
                        <div style="background: #f1f5f9; height: 6px; border-radius: 3px; overflow: hidden;">
                            <div style="background: #3b82f6; height: 100%; width: ${activeRate}%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                </div>
            `;

            if (statusChart) {
                statusChart.destroy();
            }

            statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(statusCounts).map(status => statusLabels[status]),
                    datasets: [{
                        data: Object.values(statusCounts),
                        backgroundColor: Object.keys(statusCounts).map(status => statusColors[status]),
                        borderWidth: 3,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((context.parsed / total) * 100);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function refreshStatusChart() {
            updateStatusChart();
            showNotification('Status chart refreshed!');
        }

        // Delivery Time Analysis Chart
        function updateDeliveryTimeChart() {
            const ctx = document.getElementById('deliveryTimeChart').getContext('2d');

            // Calculate delivery times for completed orders
            const deliveredOrders = allOrders.filter(order => order.status === 'delivered');
            const timeRanges = {
                '< 2 hours': 0,
                '2-4 hours': 0,
                '4-8 hours': 0,
                '8-24 hours': 0,
                '> 24 hours': 0
            };

            deliveredOrders.forEach(order => {
                const orderTime = new Date(order.createdAt);
                const now = new Date();
                const hoursElapsed = (now - orderTime) / (1000 * 60 * 60);

                if (hoursElapsed < 2) timeRanges['< 2 hours']++;
                else if (hoursElapsed < 4) timeRanges['2-4 hours']++;
                else if (hoursElapsed < 8) timeRanges['4-8 hours']++;
                else if (hoursElapsed < 24) timeRanges['8-24 hours']++;
                else timeRanges['> 24 hours']++;
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(timeRanges),
                    datasets: [{
                        label: 'Orders',
                        data: Object.values(timeRanges),
                        backgroundColor: [
                            '#10b981',
                            '#3b82f6',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ],
                        borderRadius: 4,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff'
                        }
                    },
                    scales: {
                        x: {
                            grid: { display: false },
                            ticks: { color: '#64748b', font: { size: 11 } }
                        },
                        y: {
                            beginAtZero: true,
                            grid: { color: '#f1f5f9' },
                            ticks: { color: '#64748b', stepSize: 1 }
                        }
                    }
                }
            });
        }

        // Peak Hours Chart
        function updatePeakHoursChart() {
            const ctx = document.getElementById('peakHoursChart').getContext('2d');

            const hourCounts = new Array(24).fill(0);

            allOrders.forEach(order => {
                const hour = new Date(order.createdAt).getHours();
                hourCounts[hour]++;
            });

            const labels = hourCounts.map((_, index) => {
                const hour = index === 0 ? 12 : index > 12 ? index - 12 : index;
                const period = index < 12 ? 'AM' : 'PM';
                return `${hour}${period}`;
            });

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Orders',
                        data: hourCounts,
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#8b5cf6',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff'
                        }
                    },
                    scales: {
                        x: {
                            grid: { display: false },
                            ticks: {
                                color: '#64748b',
                                font: { size: 10 },
                                maxTicksLimit: 12
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: { color: '#f1f5f9' },
                            ticks: { color: '#64748b', stepSize: 1 }
                        }
                    }
                }
            });
        }

        // Package Type Distribution Chart
        function updatePackageTypeChart() {
            const ctx = document.getElementById('packageTypeChart').getContext('2d');

            const typeCounts = {};
            allOrders.forEach(order => {
                const type = order.packageType || 'other';
                typeCounts[type] = (typeCounts[type] || 0) + 1;
            });

            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: Object.keys(typeCounts).map(type =>
                        type.charAt(0).toUpperCase() + type.slice(1)
                    ),
                    datasets: [{
                        data: Object.values(typeCounts),
                        backgroundColor: colors.slice(0, Object.keys(typeCounts).length),
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                usePointStyle: true,
                                color: '#64748b',
                                font: { size: 11 }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((context.parsed / total) * 100);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Utility functions
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function formatStatus(status) {
            return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            });
        }

        function getTimeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

            if (diffInHours < 1) return 'Just now';
            if (diffInHours < 24) return `${diffInHours}h ago`;
            const diffInDays = Math.floor(diffInHours / 24);
            return `${diffInDays}d ago`;
        }

        // Event handlers
        function setupEventListeners() {
            // Search functionality
            const searchBox = document.getElementById('searchBox');
            const statusFilter = document.getElementById('statusFilter');
            const dateFilter = document.getElementById('dateFilter');

            searchBox.addEventListener('input', filterOrders);
            statusFilter.addEventListener('change', filterOrders);
            dateFilter.addEventListener('change', filterOrders);
        }

        function filterOrders() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;

            let filtered = allOrders.filter(order => {
                // Search filter
                const matchesSearch = !searchTerm ||
                    order.trackingId.toLowerCase().includes(searchTerm) ||
                    order.pickupAddress.toLowerCase().includes(searchTerm) ||
                    order.deliveryAddress.toLowerCase().includes(searchTerm) ||
                    order.contactNumber.includes(searchTerm);

                // Status filter
                const matchesStatus = !statusFilter || order.status === statusFilter;

                // Date filter
                let matchesDate = true;
                if (dateFilter) {
                    const orderDate = new Date(order.createdAt);
                    const now = new Date();

                    switch(dateFilter) {
                        case 'today':
                            matchesDate = orderDate.toDateString() === now.toDateString();
                            break;
                        case 'week':
                            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                            matchesDate = orderDate >= weekAgo;
                            break;
                        case 'month':
                            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                            matchesDate = orderDate >= monthAgo;
                            break;
                    }
                }

                return matchesSearch && matchesStatus && matchesDate;
            });

            displayOrders(filtered);
        }

        // Order management functions
        function viewOrderDetails(trackingId) {
            const order = allOrders.find(o => o.trackingId === trackingId);
            if (!order) return;

            alert(`Order Details:\n\nTracking ID: ${order.trackingId}\nStatus: ${formatStatus(order.status)}\nFrom: ${order.pickupAddress}\nTo: ${order.deliveryAddress}\nAmount: ₹${order.estimatedCost}\nContact: ${order.contactNumber}`);
        }

        async function updateOrderStatus(trackingId) {
            const newStatus = prompt('Enter new status:\n1. order_placed\n2. picked_up\n3. in_transit\n4. delivered');

            const validStatuses = ['order_placed', 'picked_up', 'in_transit', 'delivered'];
            if (!validStatuses.includes(newStatus)) {
                showNotification('Invalid status', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/orders/${trackingId}/status`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ status: newStatus })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Status updated successfully!');
                    loadOrders();
                } else {
                    showNotification('Failed to update status', 'error');
                }
            } catch (error) {
                showNotification('Error updating status', 'error');
            }
        }

        // Utility functions
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function formatStatus(status) {
            return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        // Additional functions
        function exportData() {
            const csvContent = "data:text/csv;charset=utf-8,"
                + "Tracking ID,Pickup Address,Delivery Address,Contact,Weight,Amount,Status,Date\n"
                + allOrders.map(order =>
                    `${order.trackingId},"${order.pickupAddress}","${order.deliveryAddress}",${order.contactNumber},${order.weight},${order.estimatedCost},${order.status},${order.createdAt}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `quickdeliver_orders_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Orders exported successfully!');
        }

        function refreshData() {
            showNotification('Refreshing data...');
            loadOrders();
        }

        function showSection(section) {
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update header title
            document.querySelector('.header-title').textContent =
                section.charAt(0).toUpperCase() + section.slice(1);

            currentSection = section;
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'error' ? '#ef4444' : '#10b981'};
                color: white;
                border-radius: 8px;
                z-index: 3000;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                font-weight: 500;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, 300);
                }
            }, 3000);
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            loadOrders();
        }, 30000);

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
