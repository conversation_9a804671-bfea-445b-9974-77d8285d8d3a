<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - QuickDeliver</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: #f5f7fa;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }

        .admin-header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
        }

        .admin-logo i {
            margin-right: 10px;
            font-size: 28px;
        }

        .admin-nav {
            display: flex;
            gap: 20px;
        }

        .admin-nav button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .admin-nav button:hover {
            background: rgba(255,255,255,0.3);
        }

        .admin-container {
            max-width: 1200px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }

        .dashboard-title {
            margin-bottom: 30px;
        }

        .dashboard-title h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
        }

        .dashboard-subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-card:nth-child(1) .stat-icon { color: #3498db; }
        .stat-card:nth-child(2) .stat-icon { color: #2ecc71; }
        .stat-card:nth-child(3) .stat-icon { color: #f39c12; }
        .stat-card:nth-child(4) .stat-icon { color: #e74c3c; }

        .stat-number {
            font-size: 2.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
            font-weight: 500;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            padding: 30px;
        }

        .chart-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .orders-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            padding: 0 30px;
            padding-top: 20px;
        }

        .search-box {
            flex: 1;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .filter-select {
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            background: white;
            cursor: pointer;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 18px 30px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            color: #6c757d;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-order_placed { background: #fff3cd; color: #856404; }
        .status-picked_up { background: #d4edda; color: #155724; }
        .status-in_transit { background: #cce7ff; color: #004085; }
        .status-delivered { background: #d1ecf1; color: #0c5460; }

        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .refresh-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: rotate(180deg);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .admin-nav {
                display: none;
            }

            th, td {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Enhanced Admin Header -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-logo">
                <i class="fas fa-tachometer-alt"></i>
                <span>QuickDeliver Admin</span>
            </div>
            <nav class="admin-nav">
                <button onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button onclick="exportData()">
                    <i class="fas fa-download"></i> Export
                </button>
                <button onclick="window.location.href='/'">
                    <i class="fas fa-home"></i> Main Site
                </button>
            </nav>
        </div>
    </header>

    <div class="admin-container">
        <!-- Dashboard Title -->
        <div class="dashboard-title">
            <h1>Dashboard Overview</h1>
            <p class="dashboard-subtitle">Monitor your delivery operations in real-time</p>
        </div>

        <!-- Enhanced Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-number" id="totalOrders">0</div>
                <div class="stat-label">Total Orders</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="stat-number" id="totalRevenue">₹0</div>
                <div class="stat-label">Total Revenue</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-number" id="activeDeliveries">0</div>
                <div class="stat-label">Active Deliveries</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number" id="completedToday">0</div>
                <div class="stat-label">Completed Today</div>
            </div>
        </div>

        <!-- Dashboard Grid with Charts -->
        <div class="dashboard-grid">
            <div class="chart-container">
                <h3 class="chart-title">Orders Over Time</h3>
                <canvas id="ordersChart" width="400" height="200"></canvas>
            </div>
            <div class="chart-container">
                <h3 class="chart-title">Status Distribution</h3>
                <canvas id="statusChart" width="300" height="300"></canvas>
            </div>
        </div>

        <!-- Enhanced Orders Table -->
        <div class="orders-table">
            <div class="table-header">
                <h2>Recent Orders</h2>
                <button class="refresh-btn" onclick="refreshData()" title="Refresh Data">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>

            <!-- Search and Filter Controls -->
            <div class="table-controls">
                <input type="text" class="search-box" id="searchBox" placeholder="Search by tracking ID, address, or contact...">
                <select class="filter-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="order_placed">Order Placed</option>
                    <option value="picked_up">Picked Up</option>
                    <option value="in_transit">In Transit</option>
                    <option value="delivered">Delivered</option>
                </select>
                <select class="filter-select" id="dateFilter">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                </select>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Tracking ID</th>
                        <th>Pickup</th>
                        <th>Delivery</th>
                        <th>Contact</th>
                        <th>Weight</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="ordersTableBody">
                    <!-- Orders will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Order Detail Modal -->
        <div id="orderModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="closeOrderModal()">&times;</span>
                <h2>Order Details</h2>
                <div id="orderDetails">
                    <!-- Order details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let allOrders = [];
        let ordersChart = null;
        let statusChart = null;

        async function loadOrders() {
            try {
                const response = await fetch('/api/orders');
                const data = await response.json();

                if (data.success) {
                    allOrders = data.orders;
                    displayOrders(allOrders);
                    updateStats(allOrders);
                    updateCharts(allOrders);
                }
            } catch (error) {
                console.error('Error loading orders:', error);
                showNotification('Error loading orders', 'error');
            }
        }

        function displayOrders(orders) {
            const tbody = document.getElementById('ordersTableBody');
            tbody.innerHTML = '';

            if (orders.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: #999;">No orders found</td></tr>';
                return;
            }

            orders.forEach(order => {
                const row = document.createElement('tr');
                const statusClass = `status-${order.status}`;

                row.innerHTML = `
                    <td><strong>${order.trackingId}</strong></td>
                    <td title="${order.pickupAddress}">${truncateText(order.pickupAddress, 25)}</td>
                    <td title="${order.deliveryAddress}">${truncateText(order.deliveryAddress, 25)}</td>
                    <td>${order.contactNumber}</td>
                    <td>${order.weight} kg</td>
                    <td>₹${order.estimatedCost}</td>
                    <td><span class="status-badge ${statusClass}">${formatStatus(order.status)}</span></td>
                    <td>${formatDate(order.createdAt)}</td>
                    <td>
                        <button class="action-btn" onclick="viewOrderDetails('${order.trackingId}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn" onclick="updateOrderStatus('${order.trackingId}')" title="Update Status" style="margin-left: 5px;">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateStats(orders) {
            const totalOrders = orders.length;
            const totalRevenue = orders.reduce((sum, order) => sum + order.estimatedCost, 0);
            const activeDeliveries = orders.filter(order =>
                order.status === 'picked_up' || order.status === 'in_transit'
            ).length;
            const completedToday = orders.filter(order => {
                const today = new Date().toDateString();
                return new Date(order.createdAt).toDateString() === today && order.status === 'delivered';
            }).length;

            document.getElementById('totalOrders').textContent = totalOrders;
            document.getElementById('totalRevenue').textContent = `₹${totalRevenue.toLocaleString()}`;
            document.getElementById('activeDeliveries').textContent = activeDeliveries;
            document.getElementById('completedToday').textContent = completedToday;
        }

        function updateCharts(orders) {
            updateOrdersChart(orders);
            updateStatusChart(orders);
        }

        function updateOrdersChart(orders) {
            const ctx = document.getElementById('ordersChart').getContext('2d');

            // Group orders by date
            const ordersByDate = {};
            orders.forEach(order => {
                const date = new Date(order.createdAt).toLocaleDateString();
                ordersByDate[date] = (ordersByDate[date] || 0) + 1;
            });

            const labels = Object.keys(ordersByDate).slice(-7); // Last 7 days
            const data = labels.map(date => ordersByDate[date] || 0);

            if (ordersChart) {
                ordersChart.destroy();
            }

            ordersChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Orders',
                        data: data,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function updateStatusChart(orders) {
            const ctx = document.getElementById('statusChart').getContext('2d');

            const statusCounts = {
                'order_placed': 0,
                'picked_up': 0,
                'in_transit': 0,
                'delivered': 0
            };

            orders.forEach(order => {
                statusCounts[order.status] = (statusCounts[order.status] || 0) + 1;
            });

            if (statusChart) {
                statusChart.destroy();
            }

            statusChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Order Placed', 'Picked Up', 'In Transit', 'Delivered'],
                    datasets: [{
                        data: [
                            statusCounts.order_placed,
                            statusCounts.picked_up,
                            statusCounts.in_transit,
                            statusCounts.delivered
                        ],
                        backgroundColor: [
                            '#ffc107',
                            '#17a2b8',
                            '#6f42c1',
                            '#28a745'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Utility functions
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function formatStatus(status) {
            return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        // Order management functions
        function viewOrderDetails(trackingId) {
            const order = allOrders.find(o => o.trackingId === trackingId);
            if (!order) return;

            const orderDetails = document.getElementById('orderDetails');
            orderDetails.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Order Information</h4>
                        <p><strong>Tracking ID:</strong> ${order.trackingId}</p>
                        <p><strong>Status:</strong> <span class="status-badge status-${order.status}">${formatStatus(order.status)}</span></p>
                        <p><strong>Package Type:</strong> ${order.packageType}</p>
                        <p><strong>Weight:</strong> ${order.weight} kg</p>
                        <p><strong>Cost:</strong> ₹${order.estimatedCost}</p>
                        <p><strong>Order Date:</strong> ${formatDate(order.createdAt)}</p>
                        <p><strong>Last Updated:</strong> ${formatDate(order.updatedAt)}</p>
                    </div>
                    <div>
                        <h4>Delivery Details</h4>
                        <p><strong>Pickup Address:</strong><br>${order.pickupAddress}</p>
                        <p><strong>Delivery Address:</strong><br>${order.deliveryAddress}</p>
                        <p><strong>Contact Number:</strong> ${order.contactNumber}</p>
                    </div>
                </div>
                <div style="margin-top: 20px; text-align: center;">
                    <button class="action-btn" onclick="updateOrderStatus('${order.trackingId}')" style="margin-right: 10px;">
                        <i class="fas fa-edit"></i> Update Status
                    </button>
                    <button class="action-btn" onclick="closeOrderModal()">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
            `;

            document.getElementById('orderModal').style.display = 'block';
        }

        function closeOrderModal() {
            document.getElementById('orderModal').style.display = 'none';
        }

        async function updateOrderStatus(trackingId) {
            const newStatus = prompt('Enter new status (order_placed, picked_up, in_transit, delivered):');
            if (!newStatus) return;

            const validStatuses = ['order_placed', 'picked_up', 'in_transit', 'delivered'];
            if (!validStatuses.includes(newStatus)) {
                alert('Invalid status. Please use: ' + validStatuses.join(', '));
                return;
            }

            try {
                const response = await fetch(`/api/orders/${trackingId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ status: newStatus })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('Order status updated successfully!');
                    loadOrders();
                    closeOrderModal();
                } else {
                    showNotification('Failed to update order status: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('Error updating order status:', error);
                showNotification('Error updating order status', 'error');
            }
        }

        function exportData() {
            const csvContent = "data:text/csv;charset=utf-8,"
                + "Tracking ID,Pickup Address,Delivery Address,Contact,Weight,Amount,Status,Date\n"
                + allOrders.map(order =>
                    `${order.trackingId},"${order.pickupAddress}","${order.deliveryAddress}",${order.contactNumber},${order.weight},${order.estimatedCost},${order.status},${order.createdAt}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `orders_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Data exported successfully!');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                background: ${type === 'error' ? '#e74c3c' : '#2ecc71'};
                color: white;
                border-radius: 5px;
                z-index: 3000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                font-weight: 500;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 3000);
        }

        // Search and filter functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchBox = document.getElementById('searchBox');
            const statusFilter = document.getElementById('statusFilter');
            const dateFilter = document.getElementById('dateFilter');

            function filterOrders() {
                let filteredOrders = [...allOrders];

                // Search filter
                const searchTerm = searchBox.value.toLowerCase();
                if (searchTerm) {
                    filteredOrders = filteredOrders.filter(order =>
                        order.trackingId.toLowerCase().includes(searchTerm) ||
                        order.pickupAddress.toLowerCase().includes(searchTerm) ||
                        order.deliveryAddress.toLowerCase().includes(searchTerm) ||
                        order.contactNumber.includes(searchTerm)
                    );
                }

                // Status filter
                const statusValue = statusFilter.value;
                if (statusValue) {
                    filteredOrders = filteredOrders.filter(order => order.status === statusValue);
                }

                // Date filter
                const dateValue = dateFilter.value;
                if (dateValue) {
                    const now = new Date();
                    filteredOrders = filteredOrders.filter(order => {
                        const orderDate = new Date(order.createdAt);
                        switch(dateValue) {
                            case 'today':
                                return orderDate.toDateString() === now.toDateString();
                            case 'week':
                                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                                return orderDate >= weekAgo;
                            case 'month':
                                const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                                return orderDate >= monthAgo;
                            default:
                                return true;
                        }
                    });
                }

                displayOrders(filteredOrders);
            }

            searchBox.addEventListener('input', filterOrders);
            statusFilter.addEventListener('change', filterOrders);
            dateFilter.addEventListener('change', filterOrders);
        });

        function refreshData() {
            loadOrders();
            showNotification('Data refreshed successfully!');
        }

        // Load data on page load
        window.addEventListener('load', loadOrders);

        // Auto-refresh every 30 seconds
        setInterval(loadOrders, 30000);
    </script>
</body>
</html>
