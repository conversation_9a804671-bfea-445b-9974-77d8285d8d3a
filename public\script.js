// DOM Elements
const bookingModal = document.getElementById('bookingModal');
const trackingModal = document.getElementById('trackingModal');
const bookingForm = document.getElementById('bookingForm');
const trackingForm = document.getElementById('trackingForm');
const estimatedCostElement = document.getElementById('estimatedCost');

// Modal Functions
function openBookingModal() {
    bookingModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeBookingModal() {
    bookingModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function openTrackingModal() {
    trackingModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeTrackingModal() {
    trackingModal.style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('trackingResult').style.display = 'none';
}

// Close modals when clicking outside
window.onclick = function(event) {
    if (event.target === bookingModal) {
        closeBookingModal();
    }
    if (event.target === trackingModal) {
        closeTrackingModal();
    }
}

// Price Calculation - Updated to use backend API
async function calculatePrice() {
    const weight = parseFloat(document.getElementById('weight').value) || 0;
    const packageType = document.getElementById('packageType').value;

    if (weight <= 0 || !packageType) {
        estimatedCostElement.textContent = '--';
        return;
    }

    try {
        const response = await fetch('/api/calculate-cost', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                weight: weight,
                packageType: packageType,
                distance: 5 // Default distance, could be made dynamic later
            })
        });

        const data = await response.json();

        if (data.success) {
            estimatedCostElement.textContent = data.cost;
        } else {
            estimatedCostElement.textContent = '--';
        }
    } catch (error) {
        console.error('Error calculating price:', error);
        // Fallback to client-side calculation
        let basePrice = 50;
        let weightMultiplier = weight * 10;
        let typeMultiplier = 1;
        let distanceMultiplier = 5 * 2;

        switch(packageType) {
            case 'electronics':
                typeMultiplier = 1.5;
                break;
            case 'food':
                typeMultiplier = 1.2;
                break;
            case 'documents':
                typeMultiplier = 0.8;
                break;
            default:
                typeMultiplier = 1;
        }

        const totalPrice = Math.round(basePrice + weightMultiplier * typeMultiplier + distanceMultiplier);
        estimatedCostElement.textContent = totalPrice;
    }
}

// Event Listeners - Updated for async price calculation
document.getElementById('weight').addEventListener('input', debounce(calculatePrice, 500));
document.getElementById('packageType').addEventListener('change', calculatePrice);

// Debounce function to limit API calls while typing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Form Submissions
bookingForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Booking...';
    submitButton.disabled = true;

    const formData = {
        pickupAddress: document.getElementById('pickupAddress').value.trim(),
        deliveryAddress: document.getElementById('deliveryAddress').value.trim(),
        weight: document.getElementById('weight').value,
        packageType: document.getElementById('packageType').value,
        contactNumber: document.getElementById('contactNumber').value.trim(),
        estimatedCost: estimatedCostElement.textContent
    };

    // Basic validation
    if (!formData.pickupAddress || !formData.deliveryAddress || !formData.weight ||
        !formData.packageType || !formData.contactNumber) {
        alert('Please fill in all required fields');
        submitButton.textContent = originalText;
        submitButton.disabled = false;
        return;
    }

    if (parseFloat(formData.weight) <= 0) {
        alert('Please enter a valid weight');
        submitButton.textContent = originalText;
        submitButton.disabled = false;
        return;
    }

    try {
        const response = await fetch('/api/book-delivery', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            alert(`Booking successful! Your tracking ID is: ${result.trackingId}\n\nPlease save this tracking ID to track your package.`);
            closeBookingModal();
            bookingForm.reset();
            estimatedCostElement.textContent = '--';
        } else {
            alert('Booking failed: ' + (result.message || 'Unknown error occurred'));
        }
    } catch (error) {
        console.error('Booking error:', error);
        alert('Error: Unable to book delivery. Please check your connection and try again.');
    } finally {
        // Reset button state
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
});

trackingForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    const trackingId = document.getElementById('trackingId').value.trim();

    if (!trackingId) {
        alert('Please enter a tracking ID');
        return;
    }

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Tracking...';
    submitButton.disabled = true;

    try {
        const response = await fetch(`/api/track/${encodeURIComponent(trackingId)}`);
        const result = await response.json();

        if (result.success && result.order) {
            displayTrackingResult(result.order);
        } else {
            alert(result.message || 'Order not found. Please check your tracking ID.');
            document.getElementById('trackingResult').style.display = 'none';
        }
    } catch (error) {
        console.error('Tracking error:', error);
        alert('Error: Unable to track package. Please check your connection and try again.');
        document.getElementById('trackingResult').style.display = 'none';
    } finally {
        // Reset button state
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    }
});

// Function to display tracking results
function displayTrackingResult(order) {
    const trackingResult = document.getElementById('trackingResult');
    const statusItems = document.querySelectorAll('.status-item');

    // Status mapping
    const statusOrder = ['order_placed', 'picked_up', 'in_transit', 'delivered'];
    const currentStatusIndex = statusOrder.indexOf(order.status);

    // Update status timeline
    statusItems.forEach((item, index) => {
        if (index <= currentStatusIndex) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });

    // Add order details to the tracking result
    let orderDetailsHtml = `
        <div class="order-details" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <h4>Order Details</h4>
            <p><strong>Tracking ID:</strong> ${order.trackingId}</p>
            <p><strong>Status:</strong> ${order.statusText || order.status}</p>
            <p><strong>From:</strong> ${order.pickupAddress}</p>
            <p><strong>To:</strong> ${order.deliveryAddress}</p>
            <p><strong>Package Type:</strong> ${order.packageType}</p>
            <p><strong>Weight:</strong> ${order.weight} kg</p>
            <p><strong>Cost:</strong> ₹${order.estimatedCost}</p>
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
        </div>
    `;

    // Remove existing order details if any
    const existingDetails = trackingResult.querySelector('.order-details');
    if (existingDetails) {
        existingDetails.remove();
    }

    // Add new order details
    trackingResult.insertAdjacentHTML('beforeend', orderDetailsHtml);
    trackingResult.style.display = 'block';
}

// Smooth Scrolling
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Mobile Menu Toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', function() {
    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
});

// Add mobile menu styles
const style = document.createElement('style');
style.textContent = `
@media (max-width: 768px) {
    .nav-menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px 0;
    }
    
    .nav-menu.active li {
        margin: 10px 0;
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
}
`;
document.head.appendChild(style);
