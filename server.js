const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// In-memory storage (replace with database in production)
let orders = [];
let deliveryPartners = [];

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Book delivery endpoint
app.post('/api/book-delivery', (req, res) => {
    const {
        pickupAddress,
        deliveryAddress,
        weight,
        packageType,
        contactNumber,
        estimatedCost
    } = req.body;

    // Input validation
    if (!pickupAddress || !deliveryAddress || !weight || !packageType || !contactNumber) {
        return res.status(400).json({
            success: false,
            message: 'All fields are required'
        });
    }

    if (typeof pickupAddress !== 'string' || pickupAddress.trim().length < 5) {
        return res.status(400).json({
            success: false,
            message: 'Pickup address must be at least 5 characters long'
        });
    }

    if (typeof deliveryAddress !== 'string' || deliveryAddress.trim().length < 5) {
        return res.status(400).json({
            success: false,
            message: 'Delivery address must be at least 5 characters long'
        });
    }

    const weightNum = parseFloat(weight);
    if (isNaN(weightNum) || weightNum <= 0 || weightNum > 100) {
        return res.status(400).json({
            success: false,
            message: 'Weight must be a number between 0.1 and 100 kg'
        });
    }

    const validPackageTypes = ['documents', 'electronics', 'food', 'clothing', 'other'];
    if (!validPackageTypes.includes(packageType)) {
        return res.status(400).json({
            success: false,
            message: 'Invalid package type'
        });
    }

    const phoneRegex = /^[\d\s\-\+\(\)]{10,15}$/;
    if (!phoneRegex.test(contactNumber.replace(/\s/g, ''))) {
        return res.status(400).json({
            success: false,
            message: 'Invalid contact number format'
        });
    }

    try {
        // Generate tracking ID
        const trackingId = 'QD' + Date.now().toString() + Math.random().toString(36).substring(2, 6).toUpperCase();

        const newOrder = {
            id: orders.length + 1,
            trackingId,
            pickupAddress: pickupAddress.trim(),
            deliveryAddress: deliveryAddress.trim(),
            weight: weightNum,
            packageType,
            contactNumber: contactNumber.trim(),
            estimatedCost: parseInt(estimatedCost) || 0,
            status: 'order_placed',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        orders.push(newOrder);

        res.json({
            success: true,
            message: 'Order booked successfully',
            trackingId: trackingId,
            order: newOrder
        });
    } catch (error) {
        console.error('Error booking delivery:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again.'
        });
    }
});

// Track order endpoint
app.get('/api/track/:trackingId', (req, res) => {
    const { trackingId } = req.params;

    // Input validation
    if (!trackingId || typeof trackingId !== 'string' || trackingId.trim().length < 3) {
        return res.status(400).json({
            success: false,
            message: 'Invalid tracking ID format'
        });
    }

    try {
        const order = orders.find(o => o.trackingId === trackingId.trim());

        if (!order) {
            return res.status(404).json({
                success: false,
                message: 'Order not found. Please check your tracking ID.'
            });
        }

        // Simulate status progression based on time
        const now = new Date();
        const orderTime = new Date(order.createdAt);
        const timeDiff = (now - orderTime) / (1000 * 60); // minutes

        let status = 'order_placed';
        if (timeDiff > 30) status = 'picked_up';
        if (timeDiff > 60) status = 'in_transit';
        if (timeDiff > 120) status = 'delivered';

        order.status = status;
        order.updatedAt = new Date();

        const statusMap = {
            order_placed: 'Order Placed',
            picked_up: 'Package Picked Up',
            in_transit: 'In Transit',
            delivered: 'Delivered'
        };

        res.json({
            success: true,
            order: {
                ...order,
                statusText: statusMap[status]
            }
        });
    } catch (error) {
        console.error('Error tracking order:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again.'
        });
    }
});

// Get all orders (admin endpoint)
app.get('/api/orders', (req, res) => {
    res.json({
        success: true,
        orders: orders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    });
});

// Update order status (delivery partner endpoint)
app.put('/api/orders/:trackingId/status', (req, res) => {
    const { trackingId } = req.params;
    const { status } = req.body;
    
    const orderIndex = orders.findIndex(o => o.trackingId === trackingId);
    
    if (orderIndex === -1) {
        return res.status(404).json({
            success: false,
            message: 'Order not found'
        });
    }

    orders[orderIndex].status = status;
    orders[orderIndex].updatedAt = new Date();

    res.json({
        success: true,
        message: 'Order status updated',
        order: orders[orderIndex]
    });
});

// Calculate delivery cost endpoint
app.post('/api/calculate-cost', (req, res) => {
    const { weight, packageType, distance } = req.body;

    // Input validation
    const weightNum = parseFloat(weight);
    if (isNaN(weightNum) || weightNum <= 0 || weightNum > 100) {
        return res.status(400).json({
            success: false,
            message: 'Weight must be a number between 0.1 and 100 kg'
        });
    }

    const validPackageTypes = ['documents', 'electronics', 'food', 'clothing', 'other'];
    if (!packageType || !validPackageTypes.includes(packageType)) {
        return res.status(400).json({
            success: false,
            message: 'Invalid package type'
        });
    }

    const distanceNum = parseFloat(distance) || 5;
    if (distanceNum < 0 || distanceNum > 100) {
        return res.status(400).json({
            success: false,
            message: 'Distance must be between 0 and 100 km'
        });
    }

    try {
        let basePrice = 50;
        let weightMultiplier = weightNum * 10;
        let typeMultiplier = 1;
        let distanceMultiplier = distanceNum * 2;

        switch(packageType) {
            case 'electronics':
                typeMultiplier = 1.5;
                break;
            case 'food':
                typeMultiplier = 1.2;
                break;
            case 'documents':
                typeMultiplier = 0.8;
                break;
            default:
                typeMultiplier = 1;
        }

        const totalPrice = Math.round(basePrice + weightMultiplier * typeMultiplier + distanceMultiplier);

        res.json({
            success: true,
            cost: totalPrice,
            breakdown: {
                basePrice,
                weightCost: Math.round(weightMultiplier * typeMultiplier),
                distanceCost: distanceMultiplier
            }
        });
    } catch (error) {
        console.error('Error calculating cost:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again.'
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});

module.exports = app;
